// const StripeService = require("./StripeService");
const stripe = new (require("./StripeService"))();
const { sqlDateFormat, sqlDateTimeFormat } = require("./UtilService");
function sleep(seconds) {
  return new Promise((resolve) => {
    setTimeout(resolve, seconds * 1000);
  });
}
async function findCustomer({ customerId, sdk }, retries = 0) {
  sdk.setTable("user");
  const customerExists = await sdk.find("user", { stripe_uid: customerId });
  if (!customerExists[0]) {
    if (retries > 2) {
      return false;
    }
    console.log("waiting");
    await sleep(20);
    return await findCustomer({ customerId, sdk }, retries++);
  }
  return customerExists;
}

async function findSubscription({ subscriptionId, sdk }, retries = 0) {
  sdk.setTable("stripe_subscription");
  const subExists = await sdk.find("stripe_subscription", {
    stripe_id: subscriptionId,
  });
  if (!subExists[0]) {
    if (retries > 2) {
      return false;
    }
    console.log("waiting");
    await sleep(5);
    return await findSubscription({ subscriptionId, sdk }, retries++);
  }
  return subExists;
}

async function webhookHandledBefore({ sdk, event }) {
  sdk.setTable("stripe_webhook");
  try {
    const webhookHandledBefore = await sdk.find("stripe_webhook", {
      stripe_event_id: event.id,
      event_type: event.type,
      status: "processed",
    });
    if (webhookHandledBefore[0]) {
      return true;
    }
  } catch (error) {
    // If table doesn't exist, assume webhook hasn't been handled
    console.log(
      "stripe_webhook table not found, assuming webhook not handled before"
    );
  }
  return false;
}

async function saveWebhook({ sdk, event }) {
  sdk.setTable("stripe_webhook");
  try {
    await sdk.create("stripe_webhook", {
      stripe_event_id: event.id,
      event_type: event.type,
      object_id: event.data.object.id,
      object_type: event.data.object.object,
      livemode: event.livemode || false,
      api_version: event.api_version,
      status: "processed",
      processed_at: sqlDateTimeFormat(new Date()),
      webhook_data: JSON.stringify(event),
      created_at: sqlDateTimeFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
    });
  } catch (error) {
    // If table doesn't exist, log but don't fail the webhook
    console.log("stripe_webhook table not found, skipping webhook save");
  }
}

exports.handleCheckoutSessionCompleted = async ({ sdk, event }) => {
  /**
   * get the customer from db by the email,
   * check if the customer is new add the stripe id to their record
   * save the checkout
   * save the webhook
   */
  const checkout = event.data.object;
  const projectId = checkout.metadata.projectId;
  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);
  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  sdk.setTable("user");
  const customer = await sdk.rawQuery(
    `SELECT * FROM user WHERE stripe_uid = "${checkout.customer}" OR email = "${checkout.customer_email}" `
  );
  if (!customer[0]) {
    return "No customer was found with this email or id";
  }
  if (!customer[0].stripe_uid || customer[0].stripe_uid !== checkout.customer)
    await sdk.rawQuery(
      `UPDATE user SET stripe_uid = "${checkout.customer}" WHERE id = ${customer[0].id}`
    );
  sdk.setTable("stripe_checkout");
  await sdk.create("stripe_checkout", {
    stripe_id: checkout.id,
    user_id: customer[0].id,
    object: JSON.stringify(checkout),
    created_at: sqlDateFormat(new Date()),
    updated_at: sqlDateTimeFormat(new Date()),
  });
  await saveWebhook({ sdk, event });
  return "Checkout registered and customer updated";
};

exports.handleSubscriptionCreated = async ({ sdk, event }) => {
  /**
   * get the customer id -> find it in system if it fails first time wait for a minute and search again -> if found get his id -> save the subscription for him in system
   * get the plan by its stripe id -> if not found save it by stripe id for now
   */

  const subscription = event.data.object;
  const projectId = subscription.metadata.projectId;
  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  const customerId = subscription.customer;
  const allowRetries = 0;
  const customer = await findCustomer({ customerId, sdk }, allowRetries);

  if (!customer[0]) {
    return "Customer doesn't exist in the system";
  }

  // Handle kanglink enrollment subscriptions
  if (projectId === "kanglink") {
    const splitId = subscription.metadata.split_id;
    const athleteId = subscription.metadata.athlete_id;

    if (splitId && athleteId) {
      // Check for active enrollment to prevent duplicates
      sdk.setTable("enrollment");
      const activeEnrollment = await sdk.findOne("enrollment", {
        athlete_id: athleteId,
        split_id: splitId,
        payment_type: "subscription",
        status: "active",
      });

      // If there's already an active enrollment, don't create a new one
      if (activeEnrollment) {
        await saveWebhook({ sdk, event });
        return "Active enrollment already exists for this subscription";
      }

      // For new subscriptions, always create a new enrollment record
      // This ensures cancelled/refunded enrollments stay as historical records
      // and new subscriptions get fresh enrollment records

      // Get split data to create the enrollment
      sdk.setTable("split");
      const splitData = await sdk.findOne("split", { id: splitId });

      if (!splitData) {
        await saveWebhook({ sdk, event });
        return "Split not found for enrollment creation";
      }

      // Create new enrollment record
      const enrollmentData = {
        trainer_id: splitData.trainer_id,
        athlete_id: athleteId,
        program_id: splitData.program_id,
        split_id: splitId,
        payment_type: "subscription",
        amount: subscription.metadata.original_amount || 0,
        currency: "USD", // Default currency, could be from metadata
        enrollment_date: sqlDateTimeFormat(new Date()),
        status: "active",
        payment_status: "paid",
        stripe_subscription_id: subscription.id,
        stripe_customer_id: subscription.customer,
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
      };

      sdk.setTable("enrollment");
      const newEnrollment = await sdk.create("enrollment", enrollmentData);

      // Create commission record for new enrollment
      try {
        const CommissionService = require("../../custom/ksl_be/services/CommissionService");
        const commissionService = new CommissionService(sdk);

        const commissionEnrollmentData = {
          trainer_id: splitData.trainer_id,
          athlete_id: athleteId,
          program_id: splitData.program_id,
          split_id: splitId,
          amount: enrollmentData.amount,
          currency: enrollmentData.currency,
          affiliate_code: subscription.metadata.affiliate_code || null,
          affiliate_user_id: subscription.metadata.affiliate_user_id || null,
        };

        await commissionService.createCommissionRecord(
          newEnrollment.id,
          commissionEnrollmentData
        );

        // Mark enrollment as having commission calculated
        await sdk.updateById("enrollment", newEnrollment.id, {
          commission_calculated: true,
          updated_at: sqlDateTimeFormat(new Date()),
        });
      } catch (commissionError) {
        console.error(
          "Error creating commission record in subscription webhook:",
          commissionError
        );
        // Don't fail the webhook if commission creation fails
      }

      await saveWebhook({ sdk, event });
      return "New enrollment created for subscription";
    }
  }

  const plan = await sdk.find("stripe_price", {
    stripe_id: subscription.plan.id,
  });

  sdk.setTable("stripe_subscription");
  await sdk.create("stripe_subscription", {
    stripe_id: subscription.id,
    user_id: customer[0].id,
    price_id: plan[0]?.id || subscription.plan.id,
    status: subscription.status,
    object: JSON.stringify(subscription),
    created_at: sqlDateFormat(new Date()),
    updated_at: sqlDateTimeFormat(new Date()),
  });

  await saveWebhook({ sdk, event });
  return "New subscription added successfully";
};

exports.handleSubscriptionUpdated = async ({ sdk, event }) => {
  const subscription = event.data.object;
  const subscriptionId = event.data.object.id;
  const projectId = subscription.metadata.projectId;
  const appId = subscription.metadata?.appId;

  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  // Handle kanglink enrollment subscription updates
  if (projectId === "kanglink") {
    // Find enrollment by subscription ID
    sdk.setTable("enrollment");
    const enrollment = await sdk.findOne("enrollment", {
      stripe_subscription_id: subscriptionId,
    });

    if (enrollment) {
      let enrollmentStatus = enrollment.status;
      let paymentStatus = enrollment.payment_status;

      // Map Stripe subscription status to enrollment status
      switch (subscription.status) {
        case "active":
          enrollmentStatus = "active";
          paymentStatus = "paid";
          break;
        case "canceled":
        case "cancelled":
          enrollmentStatus = "cancelled";
          paymentStatus = "cancelled";
          break;
        case "past_due":
          enrollmentStatus = "payment_failed";
          paymentStatus = "failed";
          break;
        case "unpaid":
          enrollmentStatus = "payment_failed";
          paymentStatus = "failed";
          break;
        case "incomplete":
        case "incomplete_expired":
          enrollmentStatus = "pending";
          paymentStatus = "pending";
          break;
        default:
          // For other statuses, keep current status
          break;
      }

      // Update enrollment status based on subscription status
      await sdk.updateById("enrollment", enrollment.id, {
        status: enrollmentStatus,
        payment_status: paymentStatus,
        updated_at: sqlDateTimeFormat(new Date()),
      });
    }

    // Update stripe_subscription record
    sdk.setTable("stripe_subscription");
    const subscriptionExists = await sdk.find("stripe_subscription", {
      stripe_id: subscription.id,
    });

    if (subscriptionExists[0]) {
      await sdk.updateById("stripe_subscription", subscriptionExists[0].id, {
        status: subscription.status,
        object: JSON.stringify(subscription),
        updated_at: sqlDateTimeFormat(new Date()),
      });
    }

    await saveWebhook({ sdk, event });
    return `Kanglink subscription and enrollment updated successfully`;
  } else if (projectId === "businesspassport") {
    const customerId = subscription.customer;

    //find customer
    const customer = await findCustomer({ customerId, sdk }, skipRetries);
    const skipRetries = 3;

    //find subscription
    const subscription = await findSubscription(
      { subscriptionId, sdk },
      skipRetries
    );

    if (!subscription[0]) {
      return "Subscription doesn't exist in the system";
    }
    sdk.setTable("stripe_price");
    const plan = await sdk.find("stripe_price", {
      stripe_id: subscription.plan.id,
    });

    sdk.setTable("stripe_subscription");
    const subscriptionExists = await sdk.find("stripe_subscription", {
      stripe_id: subscription.id,
    });
    if (!subscriptionExists[0]) {
      await sdk.create("stripe_subscription", {
        stripe_id: subscription.id,
        user_id: customer[0].id,
        app_id: appId,
        price_id: plan[0]?.id || subscription.plan.id,
        status: subscription.status,
        object: JSON.stringify(subscription),
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
      });
    } else {
      await sdk.updateById("stripe_subscription", subscriptionExists[0].id, {
        price_id: plan[0]?.id || subscription.plan.id,
        object: JSON.stringify(subscription),
        status: subscription.status,
        updated_at: sqlDateTimeFormat(new Date()),
      });
    }

    await saveWebhook({ sdk, event });
    return `Subscription is updated successfully`;
  } else {
    const customerId = subscription.customer;
    const skipRetries = 3;
    const customer = await findCustomer({ customerId, sdk }, skipRetries);

    if (!customer[0]) {
      return "Customer doesn't exist in the system";
    }
    sdk.setTable("stripe_price");
    const plan = await sdk.find("stripe_price", {
      stripe_id: subscription.plan.id,
    });

    sdk.setTable("stripe_subscription");
    const subscriptionExists = await sdk.find("stripe_subscription", {
      stripe_id: subscription.id,
    });
    if (!subscriptionExists[0]) {
      await sdk.create("stripe_subscription", {
        stripe_id: subscription.id,
        user_id: customer[0].id,
        price_id: plan[0]?.id || subscription.plan.id,
        status: subscription.status,
        object: JSON.stringify(subscription),
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
      });
    } else {
      await sdk.updateById("stripe_subscription", subscriptionExists[0].id, {
        price_id: plan[0]?.id || subscription.plan.id,
        object: JSON.stringify(subscription),
        status: subscription.status,
        updated_at: sqlDateTimeFormat(new Date()),
      });
    }

    await saveWebhook({ sdk, event });
    return `Subscription of ${customer[0].email} is updated successfully`;
  }
};

exports.handleSubscriptionDeleted = async ({ sdk, event }) => {
  const subscription = event.data.object;
  const projectId = subscription.metadata.projectId;
  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  sdk.setTable("stripe_subscription");
  const subscriptionExists = await sdk.find("stripe_subscription", {
    stripe_id: subscription.id,
  });
  if (!subscriptionExists[0]) {
    return "Subscription doesn't exist in the system";
  }

  await sdk.updateById("stripe_subscription", subscriptionExists[0].id, {
    status: subscription.status,
    object: JSON.stringify(subscription),
    updated_at: sqlDateTimeFormat(new Date()),
  });

  // Handle kanglink enrollment cancellation
  if (projectId === "kanglink") {
    // Find enrollment by subscription ID and cancel it
    sdk.setTable("enrollment");
    const enrollment = await sdk.findOne("enrollment", {
      stripe_subscription_id: subscription.id,
    });

    if (enrollment) {
      await sdk.updateById("enrollment", enrollment.id, {
        status: "cancelled",
        payment_status: "cancelled",
        updated_at: sqlDateTimeFormat(new Date()),
      });
    }
  }

  const customerId = subscription.customer;
  const skipRetries = 3;
  const customer = await findCustomer({ customerId, sdk }, skipRetries);

  if (!customer[0]) {
    return "Customer doesn't exist in the system";
  }

  await saveWebhook({ sdk, event });
  return `Subscription of ${customer[0].email} is deleted successfully`;
};

exports.handleSubscriptionTrialWillEnd = async ({ sdk, event }) => {
  const subscription = event.data.object;
  const projectId = subscription.metadata.projectId;

  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);
  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";
  await saveWebhook({ sdk, event });
  return "Done";
};

exports.handlePaymentCreated = async ({ sdk, event }) => {
  const payment = event.data.object;
  const projectId = payment.metadata.projectId;
  console.log("payment_intent.created >>", payment.id);

  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  // For payment_intent.created, we just log and save the webhook
  // The actual enrollment processing happens in payment_intent.succeeded
  await saveWebhook({ sdk, event });
  return "Payment intent created - awaiting completion";
};

exports.handlePaymentFailed = async ({ sdk, event }) => {
  const payment = event.data.object;
  const projectId = payment.metadata.projectId;
  console.log("payment_intent.payment_failed >>", payment.id);

  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  // Handle kanglink enrollment payment failures
  if (
    projectId === "kanglink" &&
    payment.metadata.payment_type === "one_time"
  ) {
    const splitId = payment.metadata.split_id;
    const athleteId = payment.metadata.athlete_id;

    if (splitId && athleteId) {
      // Update enrollment payment status to failed
      sdk.setTable("enrollment");
      const enrollment = await sdk.findOne("enrollment", {
        athlete_id: athleteId,
        split_id: splitId,
        stripe_payment_intent_id: payment.id,
      });

      if (enrollment) {
        await sdk.updateById("enrollment", enrollment.id, {
          payment_status: "failed",
          status: "payment_failed",
          updated_at: sqlDateTimeFormat(new Date()),
        });

        await saveWebhook({ sdk, event });
        return "Enrollment payment failure recorded";
      }
    }
  }

  await saveWebhook({ sdk, event });
  return "Payment failure processed";
};

exports.handleInvoicePaymentSucceeded = async ({ sdk, event }) => {
  const invoice = event.data.object;
  const projectId = invoice.metadata?.projectId;
  console.log("invoice.payment_succeeded >>", invoice.id);

  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  // Handle kanglink subscription invoice payments
  if (projectId === "kanglink" && invoice.subscription) {
    const subscriptionId = invoice.subscription;

    // Find enrollment by subscription ID
    sdk.setTable("enrollment");
    const enrollment = await sdk.findOne("enrollment", {
      stripe_subscription_id: subscriptionId,
    });

    if (enrollment) {
      await sdk.updateById("enrollment", enrollment.id, {
        payment_status: "paid",
        status: "active",
        updated_at: sqlDateTimeFormat(new Date()),
      });

      await saveWebhook({ sdk, event });
      return "Subscription enrollment payment confirmed";
    }
  }

  await saveWebhook({ sdk, event });
  return "Invoice payment processed";
};

exports.handleInvoicePaymentFailed = async ({ sdk, event }) => {
  const invoice = event.data.object;
  const projectId = invoice.metadata?.projectId;
  console.log("invoice.payment_failed >>", invoice.id);

  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  // Handle kanglink subscription invoice payment failures
  if (projectId === "kanglink" && invoice.subscription) {
    const subscriptionId = invoice.subscription;

    // Find enrollment by subscription ID
    sdk.setTable("enrollment");
    const enrollment = await sdk.findOne("enrollment", {
      stripe_subscription_id: subscriptionId,
    });

    if (enrollment) {
      await sdk.updateById("enrollment", enrollment.id, {
        payment_status: "failed",
        status: "payment_failed",
        updated_at: sqlDateTimeFormat(new Date()),
      });

      await saveWebhook({ sdk, event });
      return "Subscription enrollment payment failure recorded";
    }
  }

  await saveWebhook({ sdk, event });
  return "Invoice payment failure processed";
};

exports.handlePaymentSucceeded = async ({ sdk, event }) => {
  const payment = event.data.object;
  const projectId = payment.metadata.projectId;
  console.log("payment >>", payment);
  if (!projectId) {
    return `Missing project identifier`;
  }
  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";
  const customerId = payment.customer;
  const planId = +payment.metadata.app_price_id;

  const allowRetries = 0;
  const customer = await findCustomer({ customerId, sdk }, allowRetries);
  if (!customer[0]) {
    return "Customer doesn't exist in the system";
  }

  // Handle kanglink enrollment payments
  if (
    projectId === "kanglink" &&
    payment.metadata.payment_type === "one_time"
  ) {
    const splitId = payment.metadata.split_id;
    const athleteId = payment.metadata.athlete_id;

    if (splitId && athleteId) {
      // Update enrollment payment status
      sdk.setTable("enrollment");
      const enrollment = await sdk.findOne("enrollment", {
        athlete_id: athleteId,
        split_id: splitId,
        stripe_payment_intent_id: payment.id,
      });

      if (enrollment) {
        await sdk.updateById("enrollment", enrollment.id, {
          payment_status: "paid",
          status: "active",
          updated_at: sqlDateTimeFormat(new Date()),
        });

        // Create commission record if not already created
        if (!enrollment.commission_calculated) {
          try {
            const CommissionService = require("../../custom/ksl_be/services/CommissionService");
            const commissionService = new CommissionService(sdk);

            const commissionEnrollmentData = {
              trainer_id: enrollment.trainer_id,
              athlete_id: enrollment.athlete_id,
              program_id: enrollment.program_id,
              split_id: enrollment.split_id,
              amount: enrollment.amount,
              currency: enrollment.currency,
              affiliate_code: enrollment.affiliate_code,
              affiliate_user_id: enrollment.affiliate_user_id,
            };

            await commissionService.createCommissionRecord(
              enrollment.id,
              commissionEnrollmentData
            );

            // Mark enrollment as having commission calculated
            await sdk.updateById("enrollment", enrollment.id, {
              commission_calculated: true,
              updated_at: sqlDateTimeFormat(new Date()),
            });
          } catch (commissionError) {
            console.error(
              "Error creating commission record in webhook:",
              commissionError
            );
            // Don't fail the webhook if commission creation fails
          }
        }

        await saveWebhook({ sdk, event });
        return "Enrollment payment confirmed successfully";
      }
    }
  }

  if (payment.metadata.is_lifetime_subscription === "true") {
    /**
     * A lifetime subscription payment
     * save it as a subscription
     */

    sdk.setTable("stripe_subscription");
    await sdk.create("stripe_subscription", {
      stripe_id: payment.id,
      user_id: customer[0].id,
      price_id: planId,
      status: "active",
      is_lifetime: true,
      object: JSON.stringify(payment),
      created_at: sqlDateFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
    });

    return "New lifetime subscription added successfully";
  } else if (payment.metadata.is_order === "true") {
    sdk.setTable("stripe_order");
    await sdk.create("stripe_order", {
      stripe_id: payment.id,
      user_id: customer[0].id,
      price_id: planId,
      object: JSON.stringify(payment),
      created_at: sqlDateFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
    });
  }
  await saveWebhook({ sdk, event });

  return "Order saved successfully";
};

exports.handleCustomerDiscountCreated = async ({ sdk, event }) => {
  const discount = event.data.object;
  const customerId = discount.customer;

  // Extract project ID from the coupon metadata if available
  let projectId = null;
  if (
    discount.coupon &&
    discount.coupon.metadata &&
    discount.coupon.metadata.projectId
  ) {
    projectId = discount.coupon.metadata.projectId;
  }

  // If no project ID in coupon metadata, try to find it from customer
  if (!projectId) {
    // For kanglink, we can assume the project ID
    projectId = "kanglink";
  }

  if (!projectId) {
    return `Missing project identifier`;
  }

  sdk.setProjectId(projectId);

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  // Log the discount creation for debugging
  console.log(
    `Customer discount created: ${discount.id} for customer: ${customerId}`
  );

  // Just save the webhook - the discount is already applied to the subscription
  await saveWebhook({ sdk, event });
  return "Customer discount created successfully";
};

exports.handleInvoiceCreated = async ({ sdk, event }) => {
  const invoice = event.data.object;
  const customerId = invoice.customer;
  const subscriptionId = invoice.subscription;
  const billingReason = invoice.billing_reason;
  if (billingReason === "subscription_create") {
    const subscription = await stripe.retrieveStripeSubscription({
      subscriptionId,
    });
    const projectId = subscription.metadata.projectId;
    if (!projectId) {
      return `Missing project identifier`;
    }

    sdk.setProjectId(projectId);

    const webhookHandled = await webhookHandledBefore({ sdk, event });
    if (webhookHandled) return "Webhook handled before";
    const allowRetries = 0;
    const customer = await findCustomer({ customerId, sdk }, allowRetries);
    if (!customer[0]) {
      return "Customer doesn't exist in the system";
    }

    sdk.setTable("stripe_invoice");
    await sdk.create("stripe_invoice", {
      user_id: customer[0].id,
      stripe_id: invoice.id,
      object: JSON.stringify(invoice),
      created_at: sqlDateFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
    });

    await saveWebhook({ sdk, event });
    return "New invoice saved successfully";
  }
  return "New invoice is not subscription related, ignored for now";
};

exports.handleChargeDisputed = async ({ sdk, event }) => {
  const dispute = event.data.object;
  const charge = dispute.charge;

  if (!charge) {
    return "No charge found in dispute event";
  }

  const webhookHandled = await webhookHandledBefore({ sdk, event });
  if (webhookHandled) return "Webhook handled before";

  // Find payment intent associated with this charge
  try {
    const stripe = require("./StripeService");
    const stripeService = new stripe();
    const chargeDetails = await stripeService.stripe.charges.retrieve(charge);

    if (!chargeDetails.payment_intent) {
      await saveWebhook({ sdk, event });
      return "No payment intent found for disputed charge";
    }

    const paymentIntentId = chargeDetails.payment_intent;
    const projectId = chargeDetails.metadata?.projectId;

    if (projectId === "kanglink") {
      // Find enrollment by payment intent ID
      sdk.setTable("enrollment");
      const enrollment = await sdk.findOne("enrollment", {
        stripe_payment_intent_id: paymentIntentId,
      });

      if (enrollment) {
        // Mark enrollment as disputed
        await sdk.updateById("enrollment", enrollment.id, {
          status: "disputed",
          payment_status: "disputed",
          updated_at: sqlDateTimeFormat(new Date()),
        });

        await saveWebhook({ sdk, event });
        return "Enrollment marked as disputed due to charge dispute";
      }
    }

    await saveWebhook({ sdk, event });
    return "Charge disputed but no matching enrollment found";
  } catch (error) {
    console.error("Error handling charge dispute:", error);
    await saveWebhook({ sdk, event });
    return "Error processing charge dispute";
  }
};
