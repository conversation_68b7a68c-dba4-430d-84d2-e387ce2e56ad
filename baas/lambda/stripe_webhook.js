const StripeService = require("../services/StripeService");
const StripeWebhookService = require("../services/StripeWebhookService");
const stripe = new StripeService();
const HelperService = require("../services/HelperService");
const { sqlDateFormat, sqlDateTimeFormat } = require("../services/UtilService");
const BackendSDK = require("../core/BackendSDK");

module.exports = function (app) {
  // https://stripe.com/docs/webhooks/quickstart
  // https://stripe.com/docs/webhooks/go-livehttps://stripe.com/docs/webhooks/go-live
  const config = app.get("configuration");
  const stripeSecret = config?.stripe?.secret_key;

  // Original webhook endpoint
  app.post("/v1/api/lambda/stripe/mtp/webhook", async (req, res) => {
    const sdk = app.get("sdk");

    const event = req.body;
    // sdk.setDatabase(
    //   await sdk.getProjectDatabase(event.data.object.metadata.projectId)
    // );
    sdk.setProjectId(event.data.object.metadata.projectId);

    let message = "";
    try {
      switch (event.type) {
        case "customer.subscription.created": {
          message = await StripeWebhookService.handleSubscriptionCreated({
            sdk,
            event,
          });
          break;
        }
        case "customer.subscription.deleted": {
          message = await StripeWebhookService.handleSubscriptionDeleted({
            sdk,
            event,
          });
          break;
        }
        case "customer.subscription.updated": {
          message = await StripeWebhookService.handleSubscriptionUpdated({
            sdk,
            event,
          });
          break;
        }
        case "customer.subscription.trial_will_end": {
          message = await StripeWebhookService.handleSubscriptionTrialWillEnd({
            sdk,
            event,
          });
          break;
        }
        case "invoice.created": {
          message = await StripeWebhookService.handleInvoiceCreated({
            sdk,
            event,
          });
          break;
        }
        // case "invoice.updated": {
        //   message = await StripeWebhookService.handleInvoiceCreated({ sdk });
        //   break;
        // }
        // case "invoice.finalized": {
        //   message = await StripeWebhookService.handleInvoiceCreated({  sdk });
        //   break;
        // }
        case "payment_intent.created": {
          console.log("payment_intent.created");
          message = await StripeWebhookService.handlePaymentCreated({
            sdk,
            event,
          });
          break;
        }
        case "payment_intent.succeeded": {
          console.log("payment_intent.succeeded");
          message = await StripeWebhookService.handlePaymentSucceeded({
            sdk,
            event,
          });
          break;
        }
        case "payment_intent.payment_failed": {
          console.log("payment_intent.payment_failed");
          message = await StripeWebhookService.handlePaymentFailed({
            sdk,
            event,
          });
          break;
        }
        case "invoice.payment_succeeded": {
          console.log("invoice.payment_succeeded");
          message = await StripeWebhookService.handleInvoicePaymentSucceeded({
            sdk,
            event,
          });
          break;
        }
        case "invoice.payment_failed": {
          console.log("invoice.payment_failed");
          message = await StripeWebhookService.handleInvoicePaymentFailed({
            sdk,
            event,
          });
          break;
        }
        case "checkout.session.completed": {
          message = await StripeWebhookService.handleCheckoutSessionCompleted({
            sdk,
            event,
          });
          break;
        }
        case "customer.discount.created": {
          message = await StripeWebhookService.handleCustomerDiscountCreated({
            sdk,
            event,
          });
          break;
        }
        case "charge.dispute.created": {
          console.log("charge.dispute.created");
          message = await StripeWebhookService.handleChargeDisputed({
            sdk,
            event,
          });
          break;
        }
        default:
          console.log(`Unhandled event type ${event.type}`);
      }

      res.status(200).json({ success: true, message });
    } catch (err) {
      console.log("webhooks route error: ", err);
      res.status(500).json({ success: false, error: err });
    }
  });

  // Additional webhook endpoint for dynamic project routing (v2 API)
  app.post("/v2/api/lambda/stripe/:project/webhook", async (req, res) => {
    const sdk = app.get("sdk");
    const { project } = req.params;

    const event = req.body;

    // Set project ID from URL parameter or event metadata
    const projectId = event.data.object.metadata?.projectId || project;
    sdk.setProjectId(projectId);

    let message = "";
    try {
      switch (event.type) {
        case "customer.subscription.created": {
          message = await StripeWebhookService.handleSubscriptionCreated({
            sdk,
            event,
          });
          break;
        }
        case "customer.subscription.deleted": {
          message = await StripeWebhookService.handleSubscriptionDeleted({
            sdk,
            event,
          });
          break;
        }
        case "customer.subscription.updated": {
          message = await StripeWebhookService.handleSubscriptionUpdated({
            sdk,
            event,
          });
          break;
        }
        case "customer.subscription.trial_will_end": {
          message = await StripeWebhookService.handleSubscriptionTrialWillEnd({
            sdk,
            event,
          });
          break;
        }
        case "invoice.created": {
          message = await StripeWebhookService.handleInvoiceCreated({
            sdk,
            event,
          });
          break;
        }
        case "payment_intent.created": {
          console.log("payment_intent.created");
          message = await StripeWebhookService.handlePaymentCreated({
            sdk,
            event,
          });
          break;
        }
        case "payment_intent.succeeded": {
          console.log("payment_intent.succeeded");
          message = await StripeWebhookService.handlePaymentSucceeded({
            sdk,
            event,
          });
          break;
        }
        case "payment_intent.payment_failed": {
          console.log("payment_intent.payment_failed");
          message = await StripeWebhookService.handlePaymentFailed({
            sdk,
            event,
          });
          break;
        }
        case "invoice.payment_succeeded": {
          console.log("invoice.payment_succeeded");
          message = await StripeWebhookService.handleInvoicePaymentSucceeded({
            sdk,
            event,
          });
          break;
        }
        case "invoice.payment_failed": {
          console.log("invoice.payment_failed");
          message = await StripeWebhookService.handleInvoicePaymentFailed({
            sdk,
            event,
          });
          break;
        }
        case "checkout.session.completed": {
          message = await StripeWebhookService.handleCheckoutSessionCompleted({
            sdk,
            event,
          });
          break;
        }
        case "customer.discount.created": {
          message = await StripeWebhookService.handleCustomerDiscountCreated({
            sdk,
            event,
          });
          break;
        }
        case "charge.dispute.created": {
          console.log("charge.dispute.created");
          message = await StripeWebhookService.handleChargeDisputed({
            sdk,
            event,
          });
          break;
        }
        default:
          console.log(`Unhandled event type ${event.type}`);
      }

      res.status(200).json({ success: true, message });
    } catch (err) {
      console.log("webhooks route error: ", err);
      res.status(500).json({ success: false, error: err });
    }
  });

  return [];
};
