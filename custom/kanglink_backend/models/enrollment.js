const BaseModel = require("../../../baas/core/BaseModel");

class enrollment extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "trainer_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "athlete_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "payment_type",
        type: "mapping",
        validation: "required,enum:subscription,one_time",
        defaultValue: null,
        mapping: "subscription:Subscription,one_time:One Time",
      },
      {
        name: "amount",
        type: "float",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "currency",
        type: "string",
        validation: "required",
        defaultValue: "USD",
        mapping: null,
      },
      {
        name: "enrollment_date",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "expiry_date",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required",
        defaultValue: "pending",
        mapping:
          "active:Active,expired:Expired,cancelled:Cancelled,pending:Pending,refund:Refund,disputed:Disputed",
      },
      {
        name: "payment_status",
        type: "mapping",
        validation: "required",
        defaultValue: "pending",
        mapping:
          "paid:Paid,pending:Pending,failed:Failed,refunded:Refunded,disputed:Disputed",
      },
      {
        name: "stripe_subscription_id",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_payment_intent_id",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_customer_id",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_snapshot",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "access_type",
        type: "mapping",
        validation: [],
        defaultValue: "live",
        mapping: "live:Live Updates,snapshot:Frozen Snapshot",
      },
      {
        name: "affiliate_code",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "affiliate_user_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "commission_calculated",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "original_amount",
        type: "float",
        validation: "positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "discount_amount",
        type: "float",
        validation: "min:0",
        defaultValue: 0.0,
        mapping: null,
      },
      {
        name: "discount_details",
        type: "long text",
        validation: "",
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformPaymentType(value) {
    const mappings = {
      subscription: "Subscription",
      one_time: "One Time",
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      active: "Active",
      expired: "Expired",
      cancelled: "Cancelled",
      pending: "Pending",
      refund: "Refund",
    };
    return mappings[value] || value;
  }

  transformPaymentStatus(value) {
    const mappings = {
      paid: "Paid",
      pending: "Pending",
      failed: "Failed",
      refunded: "Refunded",
    };
    return mappings[value] || value;
  }

  transformAccessType(value) {
    const mappings = {
      live: "Live Updates",
      snapshot: "Frozen Snapshot",
    };
    return mappings[value] || value;
  }
}

module.exports = enrollment;
